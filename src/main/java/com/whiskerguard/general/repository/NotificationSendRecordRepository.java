package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the NotificationSendRecord entity.
 */
@SuppressWarnings("unused")
@Repository
public interface NotificationSendRecordRepository extends JpaRepository<NotificationSendRecord, Long> {
    List<NotificationSendRecord> findByNotificationIdAndRecipientIdAndRecipientType(Long notificationId, Long recipientId, RecipientType recipientType);

    List<NotificationSendRecord> findByNotificationId(Long notificationId);

    List<NotificationSendRecord> findByNotificationIdInAndRecipientIdAndRecipientType(List<Long> notificationIds, Long recipientId, RecipientType recipientType);

    /**
     * 根据通知分类查询发送记录
     */
    @Query("SELECT nsr FROM NotificationSendRecord nsr JOIN nsr.notification n WHERE n.category = :category AND nsr.isDeleted = false")
    Page<NotificationSendRecord> findByCategory(NotificationCategory category, Pageable pageable);

    /**
     * 根据用户ID和通知分类查询发送记录
     */
    @Query("SELECT nsr FROM NotificationSendRecord nsr JOIN nsr.notification n WHERE nsr.recipientId = :userId AND n.recipientType = :category AND nsr.isDeleted = false")
    Page<NotificationSendRecord> findByUserIdAndCategory(Long userId, NotificationCategory category, Pageable pageable);
}
