package com.whiskerguard.general.repository;

import static com.whiskerguard.general.domain.CompanyTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.repository.CompanyRepository;
import jakarta.persistence.EntityManager;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for {@link CompanyRepository}.
 */
@IntegrationTest
@Transactional
public class CompanyRepositoryTest {

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private EntityManager entityManager;

    private Company company;

    @BeforeEach
    void setUp() {
        company = getCompanyRandomSampleGenerator();
    }

    @Test
    void testFindByUnifiedSocialCreditCode() {
        // Given
        companyRepository.saveAndFlush(company);

        // When
        Optional<Company> found = companyRepository.findByUnifiedSocialCreditCode(company.getUnifiedSocialCreditCode());

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getName()).isEqualTo(company.getName());
    }

    @Test
    void testFindByName() {
        // Given
        companyRepository.saveAndFlush(company);

        // When
        Optional<Company> found = companyRepository.findByName(company.getName());

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getUnifiedSocialCreditCode()).isEqualTo(company.getUnifiedSocialCreditCode());
    }

    @Test
    void testFindByRegNumber() {
        // Given
        companyRepository.saveAndFlush(company);

        // When
        Optional<Company> found = companyRepository.findByRegNumber(company.getRegNumber());

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getName()).isEqualTo(company.getName());
    }

    @Test
    void testFindByTaxNumber() {
        // Given
        companyRepository.saveAndFlush(company);

        // When
        Optional<Company> found = companyRepository.findByTaxNumber(company.getTaxNumber());

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getName()).isEqualTo(company.getName());
    }

    @Test
    void testFindByTianyanchaId() {
        // Given
        companyRepository.saveAndFlush(company);

        // When
        Optional<Company> found = companyRepository.findByTianyanchaId(company.getTianyanchaId());

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getName()).isEqualTo(company.getName());
    }

    @Test
    void testFindByNameContainingIgnoreCase() {
        // Given
        company.setName("中航重机股份有限公司");
        companyRepository.saveAndFlush(company);

        // When
        List<Company> found = companyRepository.findByNameContainingIgnoreCase("中航");

        // Then
        assertThat(found).hasSize(1);
        assertThat(found.get(0).getName()).isEqualTo(company.getName());
    }

    @Test
    void testExistsByUnifiedSocialCreditCode() {
        // Given
        companyRepository.saveAndFlush(company);

        // When
        boolean exists = companyRepository.existsByUnifiedSocialCreditCode(company.getUnifiedSocialCreditCode());

        // Then
        assertThat(exists).isTrue();
    }

    @Test
    void testExistsByName() {
        // Given
        companyRepository.saveAndFlush(company);

        // When
        boolean exists = companyRepository.existsByName(company.getName());

        // Then
        assertThat(exists).isTrue();
    }

    @Test
    void testNotFound() {
        // When
        Optional<Company> found = companyRepository.findByUnifiedSocialCreditCode("NONEXISTENT");

        // Then
        assertThat(found).isEmpty();
    }
}
