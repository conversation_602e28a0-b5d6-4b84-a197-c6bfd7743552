package com.whiskerguard.general.config;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

public class TianyanchaPropertiesTest {

    @Test
    void testDefaultValues() {
        // Given
        TianyanchaProperties properties = new TianyanchaProperties();

        // Then
        assertThat(properties.getBaseUrl()).isEqualTo("http://open.api.tianyancha.com/services/open/ic/");
        assertThat(properties.getCacheExpirationHours()).isEqualTo(168); // 7 days
        assertThat(properties.getConnectionTimeoutMs()).isEqualTo(5000);
        assertThat(properties.getReadTimeoutMs()).isEqualTo(10000);
        assertThat(properties.getMaxRetryAttempts()).isEqualTo(3);
        assertThat(properties.getRetryDelayMs()).isEqualTo(1000L);
    }

    @Test
    void testSettersAndGetters() {
        // Given
        TianyanchaProperties properties = new TianyanchaProperties();
        String apiToken = "test-token";
        String baseUrl = "http://test.api.com/";
        int cacheExpirationHours = 24;
        int connectionTimeoutMs = 3000;
        int readTimeoutMs = 8000;
        int maxRetryAttempts = 5;
        long retryDelayMs = 2000L;

        // When
        properties.setApiToken(apiToken);
        properties.setBaseUrl(baseUrl);
        properties.setCacheExpirationHours(cacheExpirationHours);
        properties.setConnectionTimeoutMs(connectionTimeoutMs);
        properties.setReadTimeoutMs(readTimeoutMs);
        properties.setMaxRetryAttempts(maxRetryAttempts);
        properties.setRetryDelayMs(retryDelayMs);

        // Then
        assertThat(properties.getApiToken()).isEqualTo(apiToken);
        assertThat(properties.getBaseUrl()).isEqualTo(baseUrl);
        assertThat(properties.getCacheExpirationHours()).isEqualTo(cacheExpirationHours);
        assertThat(properties.getConnectionTimeoutMs()).isEqualTo(connectionTimeoutMs);
        assertThat(properties.getReadTimeoutMs()).isEqualTo(readTimeoutMs);
        assertThat(properties.getMaxRetryAttempts()).isEqualTo(maxRetryAttempts);
        assertThat(properties.getRetryDelayMs()).isEqualTo(retryDelayMs);
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        TianyanchaProperties properties1 = new TianyanchaProperties();
        properties1.setApiToken("token1");
        properties1.setBaseUrl("http://api1.com/");

        TianyanchaProperties properties2 = new TianyanchaProperties();
        properties2.setApiToken("token1");
        properties2.setBaseUrl("http://api1.com/");

        TianyanchaProperties properties3 = new TianyanchaProperties();
        properties3.setApiToken("token2");
        properties3.setBaseUrl("http://api2.com/");

        // Then
        assertThat(properties1).isEqualTo(properties2);
        assertThat(properties1).isNotEqualTo(properties3);
        assertThat(properties1.hashCode()).isEqualTo(properties2.hashCode());
        assertThat(properties1.hashCode()).isNotEqualTo(properties3.hashCode());
    }
}
